<?php

namespace App\Services;

use Aws\Sns\SnsClient;
use Aws\Exception\AwsException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class SNSNotificationService
{
    protected $snsClient;
    protected $mockMode;

    public function __construct()
    {
        // Check if we should use mock mode (when AWS credentials are invalid)
        $this->mockMode = config('app.sns_mock_mode', false) || $this->shouldUseMockMode();

        if (!$this->mockMode) {
            try {
                $this->snsClient = new SnsClient([
                    'version' => 'latest',
                    'region' => config('services.sns.region'),
                    'credentials' => [
                        'key'    => config('services.sns.key'),
                        'secret' => config('services.sns.secret'),
                    ],
                ]);

                // Test the connection
                $this->testConnection();
            } catch (Exception $e) {
                Log::warning('AWS SNS connection failed, switching to mock mode: ' . $e->getMessage());
                $this->mockMode = true;
            }
        }

        if ($this->mockMode) {
            Log::info('SNS Service running in MOCK MODE - notifications will be simulated');
        }
    }

    /**
     * Check if we should use mock mode based on configuration
     */
    private function shouldUseMockMode()
    {
        $key = config('services.sns.key');
        $secret = config('services.sns.secret');
        $region = config('services.sns.region');

        return empty($key) || empty($secret) || empty($region);
    }

    /**
     * Test AWS connection
     */
    private function testConnection()
    {
        if ($this->mockMode) {
            return true;
        }

        try {
            $this->snsClient->listTopics(['MaxItems' => 1]);
            return true;
        } catch (AwsException $e) {
            if ($e->getAwsErrorCode() === 'InvalidClientTokenId') {
                throw new Exception('Invalid AWS credentials');
            }
            throw $e;
        }
    }

    /**
     * Fetch FCM token from database for a given user.
     *
     * @param int $userId
     * @return \Illuminate\Support\Collection
     */
    public function getFcmToken($userId)
    {
        return DB::table('user_access')
            ->where('user_id', $userId)
            ->whereNotNull('fcm_token')
            ->select('fcm_token', 'sns_endpoint_arn')
            ->get();
    }

    /**
     * Create SNS Endpoint for the FCM token.
     *
     * @param string $fcmToken
     * @param int $userId
     * @return string|null
     */
    public function createSnsEndpoint($fcmToken, $userId)
    {
        try {
            // Validate FCM token format first
            if (!$this->isValidFcmToken($fcmToken)) {
                Log::warning("Invalid FCM token format for user {$userId}: " . substr($fcmToken, 0, 20) . '...');
                return null;
            }

            $platformArn = config('services.sns.fcm_application_arn');
            if (empty($platformArn)) {
                Log::error('AWS SNS FCM Application ARN not configured');
                return null;
            }

            // Check if endpoint already exists for this user and token
            $existingEndpoint = $this->getExistingEndpoint($userId, $fcmToken);
            if ($existingEndpoint && !empty($existingEndpoint->sns_endpoint_arn)) {
                // Validate existing endpoint
                if ($this->isEndpointValid($existingEndpoint->sns_endpoint_arn)) {
                    Log::info("Using existing valid SNS endpoint for user {$userId}");
                    return $existingEndpoint->sns_endpoint_arn;
                } else {
                    // Clean up invalid endpoint
                    Log::warning("Existing endpoint invalid for user {$userId}, creating new one");
                    $this->cleanupInvalidEndpoint($userId, $fcmToken, $existingEndpoint->sns_endpoint_arn);
                }
            }

            // Create new endpoint with retry logic
            $maxRetries = 3;
            $retryCount = 0;

            while ($retryCount < $maxRetries) {
                try {
                    $result = $this->snsClient->createPlatformEndpoint([
                        'PlatformApplicationArn' => $platformArn,
                        'Token' => $fcmToken
                    ]);

                    if (isset($result['EndpointArn'])) {
                        $endpointArn = $result['EndpointArn'];
                        $this->saveEndpointArn($userId, $fcmToken, $endpointArn);
                        Log::info("Successfully created SNS endpoint for user {$userId}: {$endpointArn}");
                        return $endpointArn;
                    }
                    break;
                } catch (AwsException $e) {
                    $retryCount++;
                    $errorCode = $e->getAwsErrorCode();

                    if ($errorCode === 'InvalidParameter' && strpos($e->getMessage(), 'Invalid token') !== false) {
                        Log::error("Invalid FCM token for user {$userId}: " . $e->getMessage());
                        // Mark token as invalid in database
                        $this->markTokenAsInvalid($userId, $fcmToken);
                        return null;
                    }

                    if ($retryCount >= $maxRetries) {
                        throw $e;
                    }

                    Log::warning("Retry {$retryCount}/{$maxRetries} for creating endpoint for user {$userId}: " . $e->getMessage());
                    sleep(1); // Wait before retry
                }
            }

            Log::error("Failed to create SNS endpoint for user {$userId} after {$maxRetries} attempts");
            return null;
        } catch (AwsException $e) {
            Log::error('AWS SNS Error creating endpoint for user ' . $userId . ': ' . $e->getAwsErrorCode() . ' - ' . $e->getMessage());
            return null;
        } catch (Exception $e) {
            Log::error('General error creating SNS endpoint for user ' . $userId . ': ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Subscribe a user's endpoint to an SNS topic.
     *
     * @param int $userId
     * @param string $topicArn
     * @return string|null Subscription ARN if successful, null otherwise
     */
    public function subscribeUserToTopic($userId, $topicArn)
    {
        try {
            $fcmToken = $this->getFcmToken($userId);
            if ($fcmToken->isEmpty()) {
                Log::error("No FCM token found for user ID: $userId");
                return null;
            }

            $endpointArn = $this->getOrCreateSnsEndpoint($userId, $fcmToken->first()->fcm_token);
            if (!$endpointArn) {
                Log::error("Failed to get/create endpoint for user ID: $userId");
                return null;
            }

            if ($this->isUserSubscribedToTopic($userId, $topicArn)) {
                Log::info("User already subscribed to topic", [
                    'UserId' => $userId,
                    'TopicArn' => $topicArn
                ]);
                return true;
            }

            $result = $this->snsClient->subscribe([
                'TopicArn' => $topicArn,
                'Protocol' => 'application',
                'Endpoint' => $endpointArn
            ]);

            $this->updateSubscriptionArn($userId, $result['SubscriptionArn']);

            Log::info("Successfully subscribed endpoint to topic", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'SubscriptionArn' => $result['SubscriptionArn']
            ]);

            return $result['SubscriptionArn'];
        } catch (AwsException $e) {
            Log::error("Error subscribing to topic", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'ErrorMessage' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Check if a user is subscribed to a topic.
     *
     * @param int $userId
     * @param string $topicArn
     * @return bool
     */
    public function isUserSubscribedToTopic($userId, $topicArn)
    {
        try {
            $fcmToken = $this->getFcmToken($userId);
            if ($fcmToken->isEmpty()) {
                return false;
            }

            $endpointArn = $this->getOrCreateSnsEndpoint($userId, $fcmToken->first()->fcm_token);
            if (!$endpointArn) {
                return false;
            }

            return $this->checkSubscriptionExists($topicArn, $endpointArn);
        } catch (AwsException $e) {
            Log::error("Error checking topic subscription", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'ErrorMessage' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Unsubscribe a user from a topic.
     *
     * @param int $userId
     * @param string $topicArn
     * @return bool
     */
    public function unsubscribeUserFromTopic($userId, $topicArn)
    {
        try {
            $fcmToken = $this->getFcmToken($userId);
            if ($fcmToken->isEmpty()) {
                return false;
            }

            $endpointArn = $this->getOrCreateSnsEndpoint($userId, $fcmToken->first()->fcm_token);
            if (!$endpointArn) {
                return false;
            }

            $subscriptionArn = $this->findSubscriptionArn($topicArn, $endpointArn);
            if (!$subscriptionArn) {
                Log::warning("No subscription found for user", [
                    'UserId' => $userId,
                    'TopicArn' => $topicArn
                ]);
                return false;
            }

            $this->snsClient->unsubscribe(['SubscriptionArn' => $subscriptionArn]);
            $this->clearSubscriptionArn($userId);

            Log::info("Successfully unsubscribed from topic", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'SubscriptionArn' => $subscriptionArn
            ]);

            return true;
        } catch (AwsException $e) {
            Log::error("Error unsubscribing from topic", [
                'UserId' => $userId,
                'TopicArn' => $topicArn,
                'ErrorMessage' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send push notification to a user using SNS.
     *
     * @param int $userId
     * @param string $title
     * @param string $message
     * @param mixed $messageId
     * @return array|null
     */
    public function sendPushNotification($userId, $title, $message, $messageId, $productId)
    {
        try {
            // Validate inputs
            if (empty($userId) || empty($title) || empty($message)) {
                Log::error("Invalid parameters for sendPushNotification", [
                    'userId' => $userId,
                    'title' => $title ? 'present' : 'empty',
                    'message' => $message ? 'present' : 'empty'
                ]);
                return null;
            }

            $deviceTokens = $this->getFcmToken($userId);
            if ($deviceTokens->isEmpty()) {
                Log::warning("No FCM tokens found for user ID: $userId");
                return null;
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($deviceTokens as $device) {
                try {
                    // Get or create endpoint ARN
                    $endpointArn = $this->getOrCreateSnsEndpoint($userId, $device->fcm_token);

                    if (!$endpointArn) {
                        Log::warning("No endpoint ARN created for user {$userId}, token: " . substr($device->fcm_token, 0, 20) . '...');
                        $failureCount++;
                        continue;
                    }

                    $payload = $this->buildNotificationPayload($title, $message, $messageId, $productId, $endpointArn);
                    $result = $this->publishNotification($payload, $endpointArn);

                    if ($result) {
                        $results[] = $result;
                        $successCount++;
                        Log::info("✅ Notification sent successfully to user {$userId}");
                    } else {
                        $failureCount++;
                        Log::warning("❌ Failed to send notification to user {$userId}");
                    }
                } catch (AwsException $e) {
                    $failureCount++;
                    Log::error("AWS SNS Error for User ID: {$userId}", [
                        'errorCode' => $e->getAwsErrorCode(),
                        'errorMessage' => $e->getAwsErrorMessage(),
                        'token' => substr($device->fcm_token, 0, 20) . '...',
                        'endpoint' => $endpointArn ?? 'none',
                    ]);
                } catch (Exception $e) {
                    $failureCount++;
                    Log::error("General error sending notification to user {$userId}: " . $e->getMessage());
                }
            }

            Log::info("Notification batch completed for user {$userId}", [
                'success' => $successCount,
                'failures' => $failureCount,
                'total' => $successCount + $failureCount
            ]);

            return $results;
        } catch (Exception $e) {
            Log::error("Critical error in sendPushNotification for user {$userId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if the endpoint exists for the user or create a new one.
     *
     * @param int $userId
     * @param string $fcmToken
     * @return string|null
     */
    public function getOrCreateSnsEndpoint($userId, $fcmToken)
    {
        try {
            // Validate inputs first
            if (empty($userId) || empty($fcmToken)) {
                Log::warning("Invalid parameters for getOrCreateSnsEndpoint: userId={$userId}, fcmToken=" . (empty($fcmToken) ? 'empty' : 'present'));
                return null;
            }

            // Validate FCM token format
            if (!$this->isValidFcmToken($fcmToken)) {
                Log::warning("Invalid FCM token format for user {$userId}");
                return null;
            }

            $record = DB::table('user_access')
                ->where('user_id', $userId)
                ->where('fcm_token', $fcmToken)
                ->first();

            if ($record && !empty($record->sns_endpoint_arn)) {
                if ($this->isEndpointValid($record->sns_endpoint_arn)) {
                    Log::info("Using existing valid SNS endpoint for user {$userId}: {$record->sns_endpoint_arn}");
                    return $record->sns_endpoint_arn;
                }

                Log::warning("Existing endpoint invalid for user {$userId}, cleaning up and creating new one");
                $this->cleanupInvalidEndpoint($userId, $fcmToken, $record->sns_endpoint_arn);
            }

            return $this->createNewEndpoint($userId, $fcmToken);
        } catch (AwsException $e) {
            Log::error("AWS SNS Error for User ID: {$userId} - " . $e->getAwsErrorCode() . ': ' . $e->getMessage());
            return null;
        } catch (Exception $e) {
            Log::error("General error for User ID: {$userId} - " . $e->getMessage());
            return null;
        }
    }


    public function getSnsEndpoint($userId, $fcmToken)
    {
        try {
            $record = DB::table('user_access')
                ->where('user_id', $userId)
                ->where('fcm_token', $fcmToken)
                ->first();

            if ($record && !empty($record->sns_endpoint_arn)) {
                if ($this->isEndpointValid($record->sns_endpoint_arn)) {
                    Log::info("Using existing SNS endpoint: {$record->sns_endpoint_arn}");
                    return $record->sns_endpoint_arn;
                }
            }

            return null;
        } catch (AwsException $e) {
            Log::error("AWS SNS Error for User ID: {$userId}, FCM Token: {$fcmToken} - " . $e->getMessage());
            return null;
        }
    }
    /**
     * Update all SNS endpoints for all users.
     *
     * @return void
     */
    public function updateAllSnsEndpoints()
    {
        try {
            $users = DB::table('user_access')
                ->whereNotNull('fcm_token')
                ->select('user_id', 'fcm_token', 'sns_endpoint_arn')
                ->get();

            if ($users->isEmpty()) {
                Log::warning("⚠️ No users with valid FCM tokens found.");
                return;
            }

            foreach ($users as $user) {
                Log::info("🔄 Updating SNS Endpoint for User ID: {$user->user_id}, Token: {$user->fcm_token}");
                $this->getOrCreateSnsEndpoint($user->user_id, $user->fcm_token);
            }

            Log::info("✅ All SNS endpoints updated successfully.");
        } catch (Exception $e) {
            Log::error("❌ Error updating SNS endpoints: " . $e->getMessage());
        }
    }

    /**
     * Send a notification to a topic.
     *
     * @param string $topicArn
     * @param string $title
     * @param string $message
     * @return mixed
     */
    public function sendTopicNotification($topicArn, $title, $message)
    {
        try {
            $payload = $this->buildTopicNotificationPayload($title, $message);

            Log::info("Publishing SNS Topic Notification", [
                'TopicArn' => $topicArn,
                'RawPayload' => json_encode($payload, JSON_PRETTY_PRINT)
            ]);

            //            $result = $this->snsClient->publish([
            //                'TopicArn' => $topicArn,
            //                'Message' => json_encode($payload, JSON_UNESCAPED_SLASHES),
            //                'MessageStructure' => 'json'
            //            ]);
            //
            //            Log::info("SNS Topic Notification Sent Successfully!", [
            //                'MessageId' => $result['MessageId'],
            //                'TopicArn' => $topicArn
            //            ]);

            //            return $result;

        } catch (AwsException $e) {
            Log::error("Error sending SNS Topic Notification", [
                'ErrorCode' => $e->getAwsErrorCode(),
                'ErrorMessage' => $e->getAwsErrorMessage(),
                'TopicArn' => $topicArn
            ]);
            return null;
        }
    }

    /**
     * Check if an endpoint is for an iOS device
     *
     * @param string $endpointArn
     * @return bool
     */
    private function isIosEndpoint($endpointArn)
    {
        return (strpos($endpointArn, '/APNS/') !== false || strpos($endpointArn, '/APNS_SANDBOX/') !== false);
    }

    /**
     * Get existing endpoint for user and token
     *
     * @param int $userId
     * @param string $fcmToken
     * @return object|null
     */
    private function getExistingEndpoint($userId, $fcmToken)
    {
        return DB::table('user_access')
            ->where('user_id', $userId)
            ->where('fcm_token', $fcmToken)
            ->first();
    }

    /**
     * Save endpoint ARN to database
     *
     * @param int $userId
     * @param string $fcmToken
     * @param string $endpointArn
     * @return void
     */
    private function saveEndpointArn($userId, $fcmToken, $endpointArn)
    {
        DB::table('user_access')
            ->updateOrInsert(
                ['user_id' => $userId, 'fcm_token' => $fcmToken],
                ['sns_endpoint_arn' => $endpointArn]
            );
    }

    /**
     * Check if subscription exists for topic and endpoint
     *
     * @param string $topicArn
     * @param string $endpointArn
     * @return bool
     */
    private function checkSubscriptionExists($topicArn, $endpointArn)
    {
        $nextToken = null;
        do {
            $params = ['TopicArn' => $topicArn];
            if ($nextToken) {
                $params['NextToken'] = $nextToken;
            }

            $result = $this->snsClient->listSubscriptionsByTopic($params);

            foreach ($result['Subscriptions'] as $subscription) {
                if (
                    $subscription['Endpoint'] === $endpointArn &&
                    $subscription['SubscriptionArn'] !== 'PendingConfirmation'
                ) {
                    return true;
                }
            }

            $nextToken = isset($result['NextToken']) ? $result['NextToken'] : null;
        } while ($nextToken);

        return false;
    }

    /**
     * Find subscription ARN for topic and endpoint
     *
     * @param string $topicArn
     * @param string $endpointArn
     * @return string|null
     */
    private function findSubscriptionArn($topicArn, $endpointArn)
    {
        $nextToken = null;
        do {
            $params = ['TopicArn' => $topicArn];
            if ($nextToken) {
                $params['NextToken'] = $nextToken;
            }

            $result = $this->snsClient->listSubscriptionsByTopic($params);

            foreach ($result['Subscriptions'] as $subscription) {
                if ($subscription['Endpoint'] === $endpointArn) {
                    return $subscription['SubscriptionArn'];
                }
            }

            $nextToken = isset($result['NextToken']) ? $result['NextToken'] : null;
        } while ($nextToken);

        return null;
    }

    /**
     * Update subscription ARN in database
     *
     * @param int $userId
     * @param string $subscriptionArn
     * @return void
     */
    private function updateSubscriptionArn($userId, $subscriptionArn)
    {
        DB::table('user_access')
            ->where('user_id', $userId)
            ->update(['sns_subscription_arn' => $subscriptionArn]);
    }

    /**
     * Clear subscription ARN from database
     *
     * @param int $userId
     * @return void
     */
    private function clearSubscriptionArn($userId)
    {
        DB::table('user_access')
            ->where('user_id', $userId)
            ->update(['sns_subscription_arn' => null]);
    }

    /**
     * Build notification payload for individual notifications
     *
     * @param string $title
     * @param string $message
     * @param mixed $messageId
     * @param string $endpointArn
     * @return array
     */
    private function buildNotificationPayload($title, $message, $messageId, $productId, $endpointArn)
    {
        $isIos = $this->isIosEndpoint($endpointArn);

        $payload = [
            'default' => 'Notification fallback body'
        ];

        // Add Android/GCM payload
        if (!$isIos) {
            $payload['GCM'] = json_encode([
                'notification' => [
                    'title' => $title,
                    'body' => $message,
                ],
                'data' => [
                    'dataGen' => 'priority message',
                    'msg_id' => (string) $messageId,
                    'product_id' => (string) $productId,
                ],
                'android' => [
                    'priority' => 'high',
                    'notification' => [
                        'sound' => 'default',
                        'title' => $title,
                        'body' => $message,
                        'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                    ],
                    'data' => [
                        'dataAndroid' => 'priority message',
                    ],
                    'ttl' => '10023.32s',
                ],
            ], JSON_UNESCAPED_SLASHES);
        }

        // Add APNS payload for iOS
        if ($isIos) {
            $apnsPayload = [
                'aps' => [
                    'alert' => [
                        'title' => $title,
                        'body' => $message,
                    ],
                    'sound' => 'default',
                    'badge' => 1,
                    'content-available' => 1,
                ],
                'msg_id' => (string) $messageId,
                'product_id' => (string) $productId,
                'custom_data' => 'priority message',
            ];

            $payload['APNS'] = json_encode($apnsPayload, JSON_UNESCAPED_SLASHES);
            $payload['APNS_SANDBOX'] = json_encode($apnsPayload, JSON_UNESCAPED_SLASHES);
        }

        return $payload;
    }

    /**
     * Build notification payload for topic notifications
     *
     * @param string $title
     * @param string $message
     * @return array
     */
    private function buildTopicNotificationPayload($title, $message)
    {
        $fcmPayload = [
            'fcmV1Message' => [
                'message' => [
                    'notification' => [
                        'title' => $title,
                        'body' => $message
                    ],
                    'android' => [
                        'priority' => 'high',
                        'notification' => [
                            'sound' => 'default',
                            'channel_id' => 'high_importance_channel',
                            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                        ],
                        'data' => [
                            'message_type' => 'notification',
                            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                        ]
                    ],
                    'apns' => [
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $title,
                                    'body' => $message
                                ],
                                'sound' => 'default',
                                'badge' => 1,
                                'content-available' => 1,
                                'mutable-content' => 1
                            ]
                        ],
                        'headers' => [
                            'apns-push-type' => 'alert',
                            'apns-priority' => '10'
                        ]
                    ],
                    'data' => [
                        'message_type' => 'notification',
                        'title' => $title,
                        'body' => $message
                    ]
                ]
            ]
        ];

        return [
            'default' => $message,
            'GCM' => json_encode($fcmPayload),
            'APNS' => json_encode(['aps' => [
                'alert' => [
                    'title' => $title,
                    'body' => $message
                ],
                'sound' => 'default'
            ]])
        ];
    }

    /**
     * Publish notification to SNS
     *
     * @param array $payload
     * @param string $endpointArn
     * @return mixed
     */
    private function publishNotification(array $payload, $endpointArn)
    {
        try {
            $platform = $this->isIosEndpoint($endpointArn) ? 'iOS' : 'Android';

            Log::info("Sending SNS Notification", [
                'endpointArn' => $endpointArn,
                'platform' => $platform,
                'mockMode' => $this->mockMode
            ]);

            // Mock mode - simulate notification publishing
            if ($this->mockMode) {
                $mockResult = [
                    'MessageId' => 'mock-message-id-' . uniqid(),
                    'ResponseMetadata' => [
                        'RequestId' => 'mock-request-id-' . uniqid()
                    ]
                ];

                Log::info("✅ Mock SNS Notification published successfully", [
                    'endpointArn' => $endpointArn,
                    'platform' => $platform,
                    'messageId' => $mockResult['MessageId']
                ]);

                return $mockResult;
            }

            $finalPayload = [
                'Message' => json_encode($payload, JSON_UNESCAPED_SLASHES),
                'TargetArn' => $endpointArn,
                'MessageStructure' => 'json'
            ];

            // Actually publish the notification
            $result = $this->snsClient->publish($finalPayload);

            Log::info("✅ SNS Notification published successfully", [
                'endpointArn' => $endpointArn,
                'platform' => $platform,
                'messageId' => $result['MessageId'] ?? 'unknown'
            ]);

            return $result;
        } catch (AwsException $e) {
            $errorCode = $e->getAwsErrorCode();

            // Handle specific AWS SNS errors
            if ($errorCode === 'EndpointDisabled') {
                Log::warning("Endpoint disabled, attempting to re-enable: {$endpointArn}");
                // Try to re-enable the endpoint
                $this->reEnableEndpoint($endpointArn, null);
                return null;
            } elseif ($errorCode === 'NotFound') {
                Log::error("Endpoint not found: {$endpointArn}");
                return null;
            } else {
                Log::error("AWS SNS publish error: {$errorCode} - " . $e->getMessage(), [
                    'endpointArn' => $endpointArn
                ]);
                return null;
            }
        } catch (Exception $e) {
            Log::error("General error publishing notification: " . $e->getMessage(), [
                'endpointArn' => $endpointArn
            ]);
            return null;
        }
    }

    /**
     * Check if endpoint is valid and enabled
     *
     * @param string $endpointArn
     * @return bool
     */
    private function isEndpointValid($endpointArn)
    {
        // In mock mode, consider all endpoints valid
        if ($this->mockMode) {
            return true;
        }

        try {
            $this->snsClient->getEndpointAttributes(['EndpointArn' => $endpointArn]);
            // Also check if endpoint is enabled for more robust validation
            return $this->isEndpointEnabled($endpointArn);
        } catch (AwsException $e) {
            Log::warning("Endpoint validation failed for {$endpointArn}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Validate FCM token format
     *
     * @param string $fcmToken
     * @return bool
     */
    private function isValidFcmToken($fcmToken)
    {
        if (empty($fcmToken) || !is_string($fcmToken)) {
            return false;
        }

        // FCM tokens are typically 152+ characters long and contain alphanumeric characters, hyphens, underscores, and colons
        if (strlen($fcmToken) < 100) {
            return false;
        }

        // Check for valid FCM token pattern (basic validation)
        if (!preg_match('/^[a-zA-Z0-9_:-]+$/', $fcmToken)) {
            return false;
        }

        return true;
    }

    /**
     * Mark FCM token as invalid in database
     *
     * @param int $userId
     * @param string $fcmToken
     * @return void
     */
    private function markTokenAsInvalid($userId, $fcmToken)
    {
        try {
            DB::table('user_access')
                ->where('user_id', $userId)
                ->where('fcm_token', $fcmToken)
                ->update([
                    'fcm_token' => null,
                    'sns_endpoint_arn' => null,
                    'updated_at' => now()
                ]);

            Log::info("Marked invalid FCM token for user {$userId}");
        } catch (Exception $e) {
            Log::error("Failed to mark token as invalid for user {$userId}: " . $e->getMessage());
        }
    }

    /**
     * Cleanup invalid endpoint with retry mechanism
     *
     * @param int $userId
     * @param string $fcmToken
     * @param string $endpointArn
     * @return void
     */
    private function cleanupInvalidEndpoint($userId, $fcmToken, $endpointArn)
    {
        Log::warning("Invalid SNS Endpoint for User ID: {$userId}, attempting recovery...");

        // First try to re-enable the endpoint with the current token
        if ($this->reEnableEndpoint($endpointArn, $fcmToken)) {
            Log::info("Successfully re-enabled endpoint for User ID: {$userId}");
            return;
        }

        // If re-enabling fails, delete the endpoint
        try {
            $this->snsClient->deleteEndpoint(['EndpointArn' => $endpointArn]);
            Log::info("Deleted invalid endpoint for User ID: {$userId}");
        } catch (AwsException $e) {
            // Endpoint might already be deleted
            Log::warning("Could not delete endpoint, may already be removed: " . $e->getMessage());
        }

        // Clear the endpoint from database
        DB::table('user_access')
            ->where('user_id', $userId)
            ->where('fcm_token', $fcmToken)
            ->update(['sns_endpoint_arn' => null]);
    }

    /**
     * Create new endpoint
     *
     * @param int $userId
     * @param string $fcmToken
     * @return string|null
     */
    private function createNewEndpoint($userId, $fcmToken)
    {
        Log::info("Creating SNS Endpoint for User ID: {$userId}, Token: " . substr($fcmToken, 0, 20) . '...');

        // Mock mode - simulate endpoint creation
        if ($this->mockMode) {
            $mockEndpointArn = "arn:aws:sns:us-east-1:306313726991:endpoint/GCM/mblion-development/mock-" . uniqid();

            DB::table('user_access')
                ->where('user_id', $userId)
                ->where('fcm_token', $fcmToken)
                ->update(['sns_endpoint_arn' => $mockEndpointArn]);

            Log::info("✅ Mock endpoint created for user {$userId}: {$mockEndpointArn}");
            return $mockEndpointArn;
        }

        // Check if endpoint already exists for this token to prevent duplicates
        $existingEndpoint = $this->findExistingEndpoint($fcmToken);
        if ($existingEndpoint) {
            Log::info("Found existing endpoint for token, updating database record", [
                'EndpointArn' => $existingEndpoint,
                'UserId' => $userId
            ]);

            DB::table('user_access')
                ->where('user_id', $userId)
                ->where('fcm_token', $fcmToken)
                ->update(['sns_endpoint_arn' => $existingEndpoint]);
            return $existingEndpoint;
        }

        $platformArn = config('services.sns.fcm_application_arn');
        $result = $this->snsClient->createPlatformEndpoint([
            'PlatformApplicationArn' => $platformArn,
            'Token' => $fcmToken
        ]);

        if (isset($result['EndpointArn'])) {
            DB::table('user_access')
                ->where('user_id', $userId)
                ->where('fcm_token', $fcmToken)
                ->update(['sns_endpoint_arn' => $result['EndpointArn']]);
            return $result['EndpointArn'];
        }

        return null;
    }

    /**
     * Check if an endpoint is enabled
     *
     * @param string $endpointArn The endpoint ARN to check
     * @return bool Whether the endpoint is enabled
     */
    public function isEndpointEnabled($endpointArn)
    {
        try {
            $result = $this->snsClient->getEndpointAttributes([
                'EndpointArn' => $endpointArn
            ]);

            return isset($result['Attributes']['Enabled']) &&
                $result['Attributes']['Enabled'] === 'true';
        } catch (\Aws\Sns\Exception\SnsException $e) {
            Log::warning('Error checking endpoint status: ' . $e->getMessage(), [
                'EndpointArn' => $endpointArn
            ]);

            return false;
        }
    }

    /**
     * Re-enable a disabled endpoint with a new token
     *
     * @param string $endpointArn The endpoint ARN to re-enable
     * @param string $token The new FCM token to use
     * @return bool Whether re-enabling was successful
     */
    public function reEnableEndpoint($endpointArn, $token)
    {
        try {
            $this->snsClient->setEndpointAttributes([
                'EndpointArn' => $endpointArn,
                'Attributes' => [
                    'Enabled' => 'true',
                    'Token' => $token
                ]
            ]);

            Log::info('Successfully re-enabled endpoint', [
                'EndpointArn' => $endpointArn
            ]);

            return true;
        } catch (\Aws\Sns\Exception\SnsException $e) {
            Log::error('Failed to re-enable endpoint: ' . $e->getMessage(), [
                'EndpointArn' => $endpointArn,
                'ErrorCode' => $e->getAwsErrorCode()
            ]);

            return false;
        }
    }

    /**
     * Check if token is already registered to prevent duplicates
     *
     * @param string $token The FCM token to check
     * @return string|null The existing endpoint ARN or null if not found
     */
    public function findExistingEndpoint($token)
    {
        try {
            $platformApplicationArn = config('services.sns.fcm_application_arn');

            // List endpoints by token
            $result = $this->snsClient->listEndpointsByPlatformApplication([
                'PlatformApplicationArn' => $platformApplicationArn,
            ]);

            Log::info("Result from listEndpointsByPlatformApplication", [
                'EndpointsCount' => count($result['Endpoints']),
                'PlatformApplicationArn' => $platformApplicationArn
            ]);

            // Check each endpoint for matching token
            foreach ($result['Endpoints'] as $endpoint) {
                if (
                    isset($endpoint['Attributes']['Token']) &&
                    $endpoint['Attributes']['Token'] === $token &&
                    isset($endpoint['Attributes']['Enabled']) &&
                    $endpoint['Attributes']['Enabled'] === 'true'
                ) {
                    return $endpoint['EndpointArn'];
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Error checking for existing endpoint: ' . $e->getMessage());
            return null;
        }
    }

    public function validateAndReenableEndpoint($userId, $existingEndpointArn, $fcmToken)
    {
        try {
            // Get endpoint attributes
            $result = $this->snsClient->getEndpointAttributes([
                'EndpointArn' => $existingEndpointArn
            ]);

            $attributes = $result['Attributes'];

            // Check if endpoint is disabled
            if (isset($attributes['Enabled']) && $attributes['Enabled'] === 'false') {
                // Re-enable the endpoint
                $this->snsClient->setEndpointAttributes([
                    'EndpointArn' => $existingEndpointArn,
                    'Attributes' => [
                        'Enabled' => 'true',
                        'Token' => $fcmToken
                    ]
                ]);

                Log::channel('stack_sns')->info("🔄 Re-enabled SNS endpoint for user {$userId}");
                return $existingEndpointArn;
            }

            // Check if token matches
            if (isset($attributes['Token']) && $attributes['Token'] !== $fcmToken) {
                // Update token
                $this->snsClient->setEndpointAttributes([
                    'EndpointArn' => $existingEndpointArn,
                    'Attributes' => [
                        'Token' => $fcmToken,
                        'Enabled' => 'true'
                    ]
                ]);

                Log::channel('stack_sns')->info("🔄 Updated token for SNS endpoint for user {$userId}");
                return $existingEndpointArn;
            }

            // Endpoint is already valid
            return $existingEndpointArn;
        } catch (\Exception $e) {
            Log::channel('stack_sns')->warning("⚠️ Failed to validate endpoint for user {$userId}, creating new one: " . $e->getMessage());

            // If validation fails, create a new endpoint
            return $this->getOrCreateSnsEndpoint($userId, $fcmToken);
        }
    }
}
