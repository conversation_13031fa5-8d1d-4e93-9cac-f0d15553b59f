<?php

namespace App\Http\Controllers\Api\v2\Authentication;

use App\Http\Requests\Api\Auth\editUserProfile;
use App\Http\Requests\Api\Auth\forgetPasswordRequest;
use App\Http\Requests\Api\Auth\resendOTPRequest;
use App\Http\Requests\Api\Auth\SignInRequest;
use App\Http\Requests\Api\Auth\SignUpRequest;
use App\Http\Requests\Api\Auth\SocialSignUpRequest;
use App\Http\Requests\Api\Auth\verifyOTPRequest;
use App\Http\Requests\FacebookLoginRequest;
use App\Http\Requests\SocialLoginRequest;
use App\Jobs\sendForgotPasswordEmail;
use App\Jobs\VerifyOTP;
use App\Models\CategoryModel;
use App\Models\SocialUsersModel;
use App\Models\TickerModel;
use App\Models\UserAccess;
use App\Models\UserModel;
use App\Services\SNSNotificationService;
use App\SocialUser;
use App\Traits\ApiResponse;
use App\Traits\GetUuidDetail;
use App\Utils\GetTokens;
use Google_Client;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use App\Traits\GetUuid;
use App\Utils\AppConstant;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\UnauthorizedException;
use Laravel\Socialite\Facades\Socialite;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Console\Helper\Helper;
use Tymon\JWTAuth\Facades\JWTAuth;
use taobig\apple\IdentityTokenChecker;
use Illuminate\Support\Facades\Mail;
use App\Mail\VerifyOTPMail;
use App\Mail\ForgotPasswordMail;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{

    use GetUuid;
    use GetUuidDetail;
    use ApiResponse;

    protected  $snsService, $snsClient;

    function __construct(SNSNotificationService $snsService)
    {

        $this->snsService = $snsService;

        // Initialize the SNS client
        $this->snsClient = new \Aws\Sns\SnsClient([
            'version' => 'latest',
            'region'  => config('services.sns.region'),
            'credentials' => [
                'key'    => config('services.sns.key'),
                'secret' => config('services.sns.secret'),
            ],
        ]);
    }

    /*
     * Register use with email, user and password field
     */

    public function signup(SignUpRequest $request)
    {
        try {
            $otp = mt_rand(1000, 9999);
            $uuid4 = Uuid::uuid4();
            $uuid = $uuid4->toString();

            DB::beginTransaction();
            $fullname = ucwords($request->fullname);

            $user = new UserModel();
            $user->uuid = $uuid;
            $user->country_id = $request->country_id;
            $user->fullname = $fullname;
            $user->email_id = $request->email_id;
            $user->password = Hash::make($request->password);
            $user->mobile_no = $request->mobile_no;
            $user->company_name = $request->company_name;
            $user->otp_no = $otp;
            $user->save();

            $category_id = CategoryModel::where('status', AppConstant::STATUS_ACTIVE)->pluck('id');

            $user->notifications()->attach($category_id);
            $user->ticker()->attach($category_id);

            $email = new VerifyOTPMail($user);
            Mail::to($user->email_id)->send($email);

            DB::commit();
        } catch (QueryException $e) {
            DB::rollback();
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        // Get Register User Detail
        $userDetail = $this->getUserDetail($user->uuid);

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.userSignupSuccess', ['name' => $fullname]));
        $this->setData('user', $userDetail->makeHidden(['country_code']));
        return response()->json($this->setResponse(), JsonResponse::HTTP_CREATED);
    }

    /*
     * Signin  with username and password
     */

    public function signin(SignInRequest $request)
    {
        try {
            DB::beginTransaction();

            $password = $request->password;
            $user = UserModel::where('email_id', strtolower($request->email_id))->first();

            if (!$user || !Hash::check($password, $user->password)) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.userSignInFailed'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_UNAUTHORIZED);
            }

            if ($user->is_email_verified == AppConstant::STATUS_INACTIVE) {
                $this->setMeta('status', AppConstant::STATUS_OK);
                $this->setMeta('message', __('apimessages.userEmailNotVerified'));
                $this->setData('user', $user);
                return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
            }

            if ($user->status == AppConstant::STATUS_INACTIVE) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.userAccountTerminated'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_UNAUTHORIZED);
            }

            $checkDeviceId = UserAccess::where('device_id', $request->device_id)->first();

            if (!$checkDeviceId) {
                $userAccess = new UserAccess();
                $userAccess->user_id = $user->id;
                $userAccess->device_id = $request->device_id;
                $userAccess->ip = $request->ip();
                $userAccess->os = $request->os;
                $userAccess->fcm_token = $request->fcm_token;
                $userAccess->status = AppConstant::STATUS_ACTIVE;
                $userAccess->save();

                // Register SNS if token exists
                if ($request->filled('fcm_token') && $this->snsService) {
                    try {
                        $snsEndpointArn = $this->snsService->createSnsEndpoint($request->fcm_token, $user->id);
                        if ($snsEndpointArn) {
                            $userAccess->sns_endpoint_arn = $snsEndpointArn;
                            $userAccess->save();
                        }
                    } catch (\Exception $e) {
                        // Log SNS errors but don't fail login
                        Log::error('SNS enable failed: ' . $e->getMessage());
                        $this->setMeta('status', AppConstant::STATUS_FAIL);
                        $this->setMeta('message', $e->getMessage());
                        return response()->json($this->setResponse(), JsonResponse::HTTP_BAD_REQUEST);
                    }
                }

                $checkDeviceId = $userAccess; // Set it here so it has an ID
            } else {
                $checkDeviceId->update([
                    'user_id' => $user->id,
                    'device_id' => $request->device_id,
                    'ip' => $request->ip(),
                    'os' => $request->os,
                    'fcm_token' => $request->fcm_token,
                    'status' => AppConstant::STATUS_ACTIVE,
                ]);

                if ($checkDeviceId->sns_endpoint_arn && $this->snsClient) {
                    try {
                        $this->snsClient->setEndpointAttributes([
                            'EndpointArn' => $checkDeviceId->sns_endpoint_arn,
                            'Attributes' => ['Enabled' => 'true'],
                        ]);
                    } catch (\Exception $e) {
                        Log::error('SNS enable failed: ' . $e->getMessage());
                        $this->setMeta('status', AppConstant::STATUS_FAIL);
                        $this->setMeta('message', $e->getMessage());
                        return response()->json($this->setResponse(), JsonResponse::HTTP_BAD_REQUEST);
                    }
                }
            }

            // Attempt JWT login
            $customClaims = ['uuid' => $user->uuid, 'user_access' => $checkDeviceId->id];
            $credentials = $request->only('email_id', 'password');

            if (!$token = JWTAuth::attempt($credentials, $customClaims)) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('jwt.jwt_not_got'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_BAD_REQUEST);
            }

            DB::commit();
        } catch (QueryException $e) {
            DB::rollBack();
            Log::error('DB QueryException: ' . $e->getMessage());
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Exception: ' . $e->getMessage());
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        // Load dashboard info
        $ticker = TickerModel::with('category')->where('user_id', $user->id)->get();
        $userDetail = $this->getUserDetail($user->uuid);
        $userDetail->token = $token;
        $name = $userDetail->fullname;

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.userSigninSuccess', ['name' => $name]));
        $this->setData('user', $userDetail->makeHidden(['country_code']));
        $this->setData('dashboard_option', $ticker);

        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function verifyOTP(verifyOTPRequest $request)
    {
        $otp = $request->otp_no;
        $userId = $request->user_id;
        try {
            DB::beginTransaction();
            $user = UserModel::where([
                'otp_no' => $otp,
                'id' => $userId,
                'status' => AppConstant::STATUS_ACTIVE,
            ])->first();

            if ($user == "") {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.incorrectOTP'));
                return response()->json($this->setResponse(), AppConstant::NOT_FOUND);
            }
            $user->is_email_verified = AppConstant::STATUS_ACTIVE;
            $user->otp_no = null;
            $user->save();

            DB::commit();
        } catch (QueryException $e) {
            DB::rollback();
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $userDetail = $this->getUserDetail($user->uuid);

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.userEmailVerified'));
        $this->setData('user', $userDetail);
        return response()->json($this->setResponse(), JsonResponse::HTTP_CREATED);
    }

    /* for resending OTP if the user is failed to varify the OTP */

    public function resendOTP(resendOTPRequest $request)
    {
        try {
            $otp = mt_rand(1000, 9999);

            $user = UserModel::where([
                'email_id' => strtolower($request->email_id),
            ])->first();
            if ($user == "") {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.userNotfound'));
                return response()->json($this->setResponse(), AppConstant::NOT_FOUND);
            }
            $user->otp_no = $otp;
            $user->save();

            // dispatch(new VerifyOTP($user))->onQueue(AppConstant::APP_QUEUE);
            $email = new VerifyOTPMail($user);
            Mail::to($user->email_id)->send($email);
        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.resendOTP'));
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }

    public function forgotPassword(forgetPasswordRequest $request)
    {
        // return 'false'; exit;
        try {
            $user = UserModel::where([
                'email_id' => strtolower($request->email_id),
                'status' => AppConstant::STATUS_ACTIVE
            ])->first();
            if (!$user) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('apimessages.emailNotFound'));
                return response()->json($this->setResponse(), AppConstant::NOT_FOUND);
            }

            $getToken = new GetTokens();
            $tokenObj = $getToken->limit(10);
            $token = $tokenObj->token;
            $uuid = $user->uuid;
            $user->forgot_password_code = $token;
            $user->save();

            $email = new ForgotPasswordMail($user, $uuid);
            Mail::to($user->email_id)->send($email);

            // dispatch(new SendForgotPasswordEmail($user, $uuid))->onQueue(AppConstant::APP_QUEUE);



        } catch (QueryException $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('auth.server_error'));
            $this->setMeta('message', $e->getMessage());
            return response()->json($this->setResponse(), AppConstant::INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('auth.server_error'));
            $this->setMeta('message', $e->getMessage());
            return response()->json($this->setResponse(), AppConstant::INTERNAL_SERVER_ERROR);
        }
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.forgot_password_mail_success'));
        return response()->json($this->setResponse(), AppConstant::OK);
    }

    // public function logout() {
    //     $token = JWTAuth::getToken();
    //     $loginUserDetail = JWTAuth::getPayload($token)->get('user_access');

    //     UserAccess::where('id', $loginUserDetail)->delete();
    //     JWTAuth::invalidate($token);

    //     $this->setMeta('status', AppConstant::STATUS_OK);
    //     $this->setMeta('message', __('apimessages.userLogout'));
    //     return response()->json($this->setResponse(), JsonResponse::HTTP_CREATED);
    // }
    /**
     * Log out the user and invalidate tokens
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        try {
            // Get the current token
            $token = JWTAuth::getToken();

            // Get user details from token
            $loginUserDetail = JWTAuth::getPayload($token)->get('user_access');

            // Get user access record
            $userAccess = UserAccess::where('id', $loginUserDetail)->first();

            if ($userAccess) {
                $userId = $userAccess->user_id;

                Log::info("User ID from access record: {$userId}");

                // Check if device_id is provided in the request
                if ($request->has('device_id') && !empty($request->device_id)) {
                    // Get the specific device for this user
                    $userDevice = UserAccess::where('user_id', $userId)
                        ->where('device_id', $request->device_id)
                        ->first();

                    Log::info("Looking for device ID: " . $request->device_id . " for user {$userId}");

                    // Process the specific device if found
                    if ($userDevice) {
                        Log::info("Found device for user {$userId}: " . $userDevice->id);

                        // Disable SNS endpoint if it exists
                        if ($userDevice->sns_endpoint_arn) {
                            try {
                                $this->snsClient->setEndpointAttributes([
                                    'EndpointArn' => $userDevice->sns_endpoint_arn,
                                    'Attributes' => [
                                        'Enabled' => 'false',
                                    ],
                                ]);
                                Log::info("Successfully disabled SNS endpoint: {$userDevice->sns_endpoint_arn}");
                            } catch (\Exception $e) {
                                // Log the error but continue with logout process
                                Log::error("SNS Error during logout: " . $e->getMessage());
                            }
                        }

                        // Update the device status to inactive
                        $userDevice->status = AppConstant::STATUS_INACTIVE;
                        $userDevice->save();

                        Log::info("Updated device status to inactive: " . $userDevice->id);
                    } else {
                        Log::warning("No device found with ID: {$request->device_id} for user {$userId}");
                    }
                } else {
                    // If no device_id provided, update the current user access record
                    // Disable SNS endpoint if it exists
                    if ($userAccess->sns_endpoint_arn) {
                        try {
                            $this->snsClient->setEndpointAttributes([
                                'EndpointArn' => $userAccess->sns_endpoint_arn,
                                'Attributes' => [
                                    'Enabled' => 'false',
                                ],
                            ]);
                            Log::info("Successfully disabled SNS endpoint: {$userAccess->sns_endpoint_arn}");
                        } catch (\Exception $e) {
                            // Log the error but continue with logout process
                            Log::error("SNS Error during logout: " . $e->getMessage());
                        }
                    }

                    // Update the status directly
                    $userAccess->status = AppConstant::STATUS_INACTIVE;
                    $userAccess->save();

                    Log::info("Updated user access status to inactive: " . $userAccess->id);
                }

                Log::info("Processed logout for user access record {$loginUserDetail}");
            } else {
                Log::warning("No user access record found for ID: {$loginUserDetail}");
            }

            // Invalidate JWT token
            JWTAuth::invalidate($token);
            Log::info("JWT token invalidated");

            $this->setMeta('status', AppConstant::STATUS_OK);
            $this->setMeta('message', __('apimessages.userLogout'));

            return response()->json($this->setResponse(), JsonResponse::HTTP_CREATED);
        } catch (\Exception $e) {
            Log::error('Logout error: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.somethingWentWrong'));

            return response()->json($this->setResponse(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    /*
     * Send User Response in signin and signup
     */

    public function getUserDetail($uuid)
    {
        return UserModel::with('country')->where(array(
            'uuid' => $uuid,
            'status' => AppConstant::STATUS_ACTIVE
        ))->first();
    }

    /**
     * @param SocialLoginRequest $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function socialLogin(Request $request)
    {
        try {
            DB::beginTransaction();
            $providerToken = $request->provider_token;
            $providerId = $request->provider_id;
            $provider = $request->provider;
            //            $providername = $request->provider == 1 ? AppConstant::PROVIDER_FACEBOOK : AppConstant::PROVIDER_GOOGLE;
            if ($request->provider == 1) {
                $providername = AppConstant::PROVIDER_FACEBOOK;
            } else if ($request->provider == 2) {
                $providername = AppConstant::PROVIDER_GOOGLE;
            } else {
                $providername = AppConstant::PROVIDER_APPLE;
            }
            switch ($provider) {
                case AppConstant::FACEBOOK:
                    $user = Socialite::driver($providername)->userFromToken($providerToken);
                    $UserproviderId = $user->id;
                    if ($UserproviderId !== $providerId) {
                        throw new ModelNotFoundException();
                    }

                    $socialUser = SocialUsersModel::with('user')->where([
                        'provider_id' => $providerId,
                        'provider' => AppConstant::FACEBOOK,
                        'status' => AppConstant::STATUS_ACTIVE
                    ])->first();

                    if (!$socialUser) {
                        throw new ModelNotFoundException();
                    }
                    break;
                case AppConstant::GOOGLE:
                    $googleClient = new Google_Client(['CLIENT_ID' => env('GOOGLE_CLIENT_ID')]);
                    $payload = $googleClient->verifyIdToken($providerToken);
                    $UserproviderId = $payload['sub'];
                    if ($UserproviderId !== $providerId) {
                        throw new ModelNotFoundException();
                    }

                    $socialUser = SocialUsersModel::with('user')->where([
                        'provider_id' => $providerId,
                        'provider' => AppConstant::GOOGLE,
                        'status' => AppConstant::STATUS_ACTIVE
                    ])->first();

                    if (!$socialUser) {
                        $this->setMeta('status', AppConstant::STATUS_FAIL);
                        $this->setMeta('message', __('message.RecordNotFound'));
                        return response()->json($this->setResponse(), AppConstant::NOT_FOUND);
                    }
                    break;
                case AppConstant::APPLE:
                    try {
                        $obj = IdentityTokenChecker::checkAppleIdentityToken($providerToken);
                    } catch (\Exception $e) {
                        $this->setMeta('status', AppConstant::STATUS_FAIL);
                        $this->setMeta('message', __('apimessages.token_mismatch'));
                        return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                    }
                    /* Check Status Code */
                    if ($obj == '') {
                        $this->setMeta('status', AppConstant::STATUS_FAIL);
                        $this->setMeta('message', __('apimessages.token_mismatch'));
                        return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                    }
                    /* Match FacebookId */
                    if (isset($obj->sub) && !empty($obj->sub)) {
                        $UserproviderId = $obj->sub;

                        $user = UserModel::where('email_id', $request->email_id)->first();
                        if (isset($user['id']) && !empty($user['id'])) {
                            $socialUser = SocialUsersModel::with('user')->where([
                                'user_id' => $user['id'],
                                'provider' => AppConstant::APPLE,
                                'status' => AppConstant::STATUS_ACTIVE
                            ])->first();
                            if (!$socialUser) {
                                $this->setMeta('status', AppConstant::STATUS_FAIL);
                                $this->setMeta('message', 'User not found please try agin');
                                return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                            }
                        } else {
                            $this->setMeta('status', AppConstant::STATUS_FAIL);
                            $this->setMeta('message', 'User not found please try agin');
                            return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                        }
                    }
                    break;
            }

            $ticker = TickerModel::with('category')->where([
                'user_id' => $socialUser->user->id
            ])->get();

            $checkDeviceId = UserAccess::where('device_id', $request->device_id)->first();
            if (!$checkDeviceId) {
                $userAccess = new UserAccess();
                if ($request->filled('fcm_token')) {
                    $userAccess->fcm_token = $request->fcm_token;
                }
                $userAccess->user_id = $socialUser->user_id;
                $userAccess->device_id = $request->device_id;
                $userAccess->ip = $request->ip();
                $userAccess->os = $request->os;
                $userAccess->save();
                $checkDeviceId = $userAccess;
                Log::info("Not found device social login v2");
                Log::info($checkDeviceId);
            } else {
                $userAcessData = [
                    'user_id' => $socialUser->user_id,
                    'device_id' => $request->device_id,
                    'ip' => $request->ip(),
                    'os' => $request->os,
                    'fcm_token' => $request->fcm_token
                ];
                $checkDeviceId->update($userAcessData);
                Log::info("Found device social login v2");
                Log::info($checkDeviceId);
            }

            /* $userAccess = new UserAccess();
              $userAccess->user_id = $socialUser->user_id;
              if ($request->filled('fcm_token')) {
              $userAccess->fcm_token = $request->fcm_token;
              }
              $userAccess->ip = $request->ip();
              $userAccess->os = $request->os;
              $userAccess->device_id = $request->device_id;
              $userAccess->save(); */

            $token = null;
            $credentials = $socialUser->user;
            $customClaims = ['uuid' => $socialUser->user->uuid, 'user_access' => $checkDeviceId->id];
            if (!$token = JWTAuth::fromUser($credentials, $customClaims)) {
                $this->setMeta('status', AppConstant::STATUS_FAIL);
                $this->setMeta('message', __('jwt.jwt_not_got'));
                return response()->json($this->setResponse(), JsonResponse::HTTP_BAD_REQUEST);
            }

            DB::commit();

            // Get Register User Detail
            $userDetail = $this->getUserDetail($socialUser->user->uuid);
            $userDetail->token = $token;
            $this->setMeta('status', AppConstant::STATUS_OK);
            $this->setMeta('message', __('apimessages.social_login_success'));
            $this->setData('user', $userDetail);
            $this->setData('dashboard_option', $ticker);
            return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
        } catch (QueryException $e) {
            DB::rollBack();
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('message.serverError'));
            return response()->json($this->setResponse(), AppConstant::INTERNAL_SERVER_ERROR);
        }
    }

    public function socialSignUp(SocialSignUpRequest $request)
    {
        //        exit('s');
        //        dd($request->provider);
        $providerToken = $request->provider_token;
        $provider = $request->provider;
        //        $providername = $request->provider == 1 ? AppConstant::PROVIDER_FACEBOOK : $request->provider == 2 ? AppConstant::PROVIDER_GOOGLE : AppConstant::PROVIDER_APPLE;
        if ($request->provider == 1) {
            $providername = AppConstant::PROVIDER_FACEBOOK;
        } else if ($request->provider == 2) {
            $providername = AppConstant::PROVIDER_GOOGLE;
        } else {
            $providername = AppConstant::PROVIDER_APPLE;
        }
        switch ($provider) {
            case AppConstant::FACEBOOK:
                $user = Socialite::driver($providername)->userFromToken($providerToken);
                $providerId = $user->id;

                $socialUser = SocialUsersModel::with('user')->where([
                    'provider_id' => $providerId,
                    'provider' => AppConstant::FACEBOOK,
                    'status' => AppConstant::STATUS_ACTIVE
                ])->first();
                //  Check Social User is already register or not
                if ($socialUser) {
                    $this->setMeta('status', AppConstant::STATUS_FAIL);
                    $this->setMeta('message', __('message .social_already_register'));
                    return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                }
                break;

            case AppConstant::GOOGLE:
                $google = new Google_Client(['CLIENT_ID' => env('GOOGLE_CLIENT_ID')]);
                $google->setApplicationName(env('APP_NAME'));
                $payload = $google->verifyIdToken($providerToken);
                $providerId = $payload['sub'];
                if ($providerId === null) {
                    $this->setMeta('status', AppConstant::STATUS_FAIL);
                    $this->setMeta('message', __('apimessages.token_mismatch'));
                    return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                }
                $socialUser = SocialUsersModel::with('user')->where([
                    'provider_id' => $providerId,
                    'provider' => AppConstant::GOOGLE,
                    'status' => AppConstant::STATUS_ACTIVE
                ])->first();
                if ($socialUser) {
                    $this->setMeta('status', AppConstant::STATUS_FAIL);
                    $this->setMeta('message', __('messages.social_already_register'));
                    return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                }
                break;
            case AppConstant::APPLE:
                //                exit($providerToken);
                try {
                    $obj = IdentityTokenChecker::checkAppleIdentityToken($providerToken);
                } catch (\Exception $e) {
                    $this->setMeta('status', AppConstant::STATUS_FAIL);
                    $this->setMeta('message', __('apimessages.token_mismatch'));
                    return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                }
                /* Check Status Code */
                if ($obj == '') {
                    $this->setMeta('status', AppConstant::STATUS_FAIL);
                    $this->setMeta('message', __('apimessages.token_mismatch'));
                    return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                }

                if (isset($obj->sub) && !empty($obj->sub)) {
                    $providerId = $obj->sub;
                    $socialUser = SocialUsersModel::with('user')->where([
                        'provider_id' => $providerId,
                        'provider' => AppConstant::APPLE,
                        'status' => AppConstant::STATUS_ACTIVE
                    ])->first();
                    if ($socialUser) {
                        $this->setMeta('status', AppConstant::STATUS_FAIL);
                        $this->setMeta('message', __('messages.social_already_register'));
                        return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
                    }
                }
                break;
        }

        try {
            if ($request->provider == AppConstant::FACEBOOK) {
                $email = $user->getEmail();
                if ($email || $request->email_id) {
                    $abc = file_get_contents($user->getAvatar());
                    $ProfileImage = ('Image/' . uniqid("IMG_", 15) . '.png');

                    Storage::disk('public')->put($ProfileImage, $abc);

                    DB::beginTransaction();

                    $uuid = Uuid::uuid4()->toString();
                    $userObj = new UserModel();
                    $userObj->uuid = $uuid;
                    $userObj->fullname = $user->user['name'];
                    $userObj->email_id = $request->email_id;
                    $userObj->profile_pic = $ProfileImage;
                    $userObj->is_social = AppConstant::STATUS_ACTIVE;
                    $userObj->is_email_verified = AppConstant::STATUS_ACTIVE;
                    $userObj->save();

                    $social = new SocialUsersModel();
                    $social->user_id = $userObj->id;
                    $social->provider = $provider;
                    $social->provider_id = $providerId;
                    $social->save();

                    DB::commit();
                }
            } else if ($request->provider == AppConstant::GOOGLE) {
                if ($request->email_id) {
                    $ProfileImage = null;
                    if (isset($payload['picture']) && !empty($payload['picture'])) {
                        $abc = file_get_contents($payload['picture']);
                        $ProfileImage = ('Image/' . uniqid("IMG_", 15) . '.png');

                        Storage::disk('public')->put($ProfileImage, $abc);
                    }
                    DB::beginTransaction();

                    $uuid = Uuid::uuid4()->toString();
                    $userObj = new UserModel();
                    $userObj->uuid = $uuid;
                    $userObj->fullname = $payload['name'];
                    $userObj->email_id = $request->email_id;
                    $userObj->profile_pic = $ProfileImage;
                    $userObj->is_social = AppConstant::STATUS_ACTIVE;
                    $userObj->is_email_verified = AppConstant::STATUS_ACTIVE;
                    $userObj->save();

                    $social = new SocialUsersModel();
                    $social->user_id = $userObj->id;
                    $social->provider = $provider;
                    $social->provider_id = $providerId;
                    $social->save();
                    DB::commit();
                }
            } else {
                $email = $obj->email;
                if ($email || $request->email_id) {
                    DB::beginTransaction();
                    $uuid = Uuid::uuid4()->toString();
                    $userObj = new UserModel();
                    $userObj->uuid = $uuid;
                    $userObj->email_id = $email;
                    $userObj->is_social = AppConstant::STATUS_ACTIVE;
                    $userObj->is_email_verified = AppConstant::STATUS_ACTIVE;
                    $userObj->save();

                    $social = new SocialUsersModel();
                    $social->user_id = $userObj->id;
                    $social->provider = $provider;
                    $social->provider_id = $providerId;
                    $social->save();
                    DB::commit();
                }
            }
        } catch (QueryException $e) {
            DB::rollBack();
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __($e->getMessage()));
            return response()->json($this->setResponse(), AppConstant::INTERNAL_SERVER_ERROR);
        } catch (ClientException $e) {
            DB::rollBack();
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', $e->getMessage());
            return response()->json($this->setResponse(), AppConstant::UNPROCESSABLE_REQUEST);
        }

        $user = UserModel::where('id', $userObj->id)->first();
        $category_id = CategoryModel::where('status', AppConstant::STATUS_ACTIVE)->pluck('id');
        $user->notifications()->attach($category_id);
        $user->ticker()->attach($category_id);

        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.facebook_success'));
        $this->setData("user", $user);
        return response()->json($this->setResponse(), AppConstant::OK);
    }

    public function editUserProfile(editUserProfile $request)
    {
        try {
            $userObj = JWTAuth::toUser($request->bearerToken());
            $uuid = $userObj->uuid;
            $user = UserModel::with('country')->where('uuid', $uuid)->first();
            if (!$user) {
                $this->setMeta('status', AppConstant::NOT_FOUND);
                $this->setMeta('message', __('apimessages.userNotfound'));
                return response()->json($this->setResponse(), AppConstant::NOT_FOUND);
            }
            $user->update($request->all());
        } catch (QueryException $e) {
            echo $e->getMessage();
            $this->setMeta('status', AppConstant::STATUS_FAIL);
            $this->setMeta('message', __('apimessages.serverError'));
            return response()->json($this->setResponse(), AppConstant::INTERNAL_SERVER_ERROR);
        }
        $userData = UserModel::with('country')->where('id', $userObj->id)->first();
        $this->setMeta('status', AppConstant::STATUS_OK);
        $this->setMeta('message', __('apimessages.profileUpdateSuccess'));
        $this->setData("user", $userData);
        return response()->json($this->setResponse(), JsonResponse::HTTP_OK);
    }
}
